// User Menu Interaction Improvements
function initializeUserMenu() {
  // Prevent multiple initializations
  if (window.userMenuInitialized) {
    console.log('User menu already initialized, skipping...');
    return;
  }

  // Set a flag to indicate that the new user menu implementation is initialized
  window.userMenuInitialized = true;

  // Initialize demo mode variables if they don't exist
  if (typeof window.isDemoMode === 'undefined') {
    window.isDemoMode = false;
  }

  if (typeof window.originalUserCompany === 'undefined') {
    window.originalUserCompany = window.userCompany || '';
  }

  // Initialize reactivation flags
  window.reactivationInProgress = false;

  // Get DOM elements
  const userMenuButton = document.getElementById('user-menu-button');
  const userMenu = document.getElementById('user-menu');
  const userMenuBackdrop = document.getElementById('user-menu-backdrop');
  const userMenuClose = document.getElementById('user-menu-close');
  const userLogout = document.getElementById('user-logout');
  const editProfile = document.getElementById('edit-profile');

  console.log('User menu elements found:', {
    button: !!userMenuButton,
    menu: !!userMenu,
    backdrop: !!userMenuBackdrop,
    close: !!userMenuClose,
    logout: !!userLogout,
    editProfile: !!editProfile
  });

  if (!userMenuButton || !userMenu || !userMenuBackdrop || !userLogout || !editProfile) {
    console.error('One or more user menu elements not found:', {
      button: !!userMenuButton,
      menu: !!userMenu,
      backdrop: !!userMenuBackdrop,
      close: !!userMenuClose,
      logout: !!userLogout,
      editProfile: !!editProfile
    });
    return;
  }

  // Function to show the menu with enhanced animation
  function showMenu() {
    console.log('showMenu called');
    userMenu.classList.remove('hidden');
    userMenuBackdrop.classList.remove('hidden');

    // Trigger reflow to ensure transitions work
    void userMenu.offsetWidth;

    // Show the menu and backdrop
    userMenu.classList.add('show');
    userMenuBackdrop.classList.add('show');

    console.log('Menu classes after show:', {
      menuHidden: userMenu.classList.contains('hidden'),
      menuShow: userMenu.classList.contains('show'),
      backdropHidden: userMenuBackdrop.classList.contains('hidden'),
      backdropShow: userMenuBackdrop.classList.contains('show')
    });

    // Prevent body scrolling when sidebar is open
    document.body.style.overflow = 'hidden';

    // Add a subtle entrance animation for menu items
    const menuItems = userMenu.querySelectorAll('a.menu-item, #edit-profile, #user-logout');
    menuItems.forEach((item, index) => {
      item.style.opacity = '0';
      item.style.transform = 'translateX(10px)';
      setTimeout(() => {
        item.style.transition = 'opacity 0.2s ease, transform 0.2s ease';
        item.style.opacity = '1';
        item.style.transform = 'translateX(0)';
      }, 50 + (index * 30));
    });
  }

  // Function to hide the menu with callback support
  function hideMenu(callback) {
    console.log('hideMenu called');
    // Start the menu closing animation
    userMenu.classList.remove('show');
    userMenuBackdrop.classList.remove('show');

    // Add a smooth exit animation for menu items
    const menuItems = userMenu.querySelectorAll('a.menu-item, #edit-profile, #user-logout');
    menuItems.forEach(item => {
      item.style.transition = 'opacity 0.2s ease, transform 0.2s ease';
      item.style.opacity = '0';
      item.style.transform = 'translateX(10px)';
    });

    // Wait for transition to complete before hiding elements
    setTimeout(() => {
      userMenu.classList.add('hidden');
      userMenuBackdrop.classList.add('hidden');

      // Reset menu item styles
      menuItems.forEach(item => {
        item.style.transition = '';
        item.style.opacity = '';
        item.style.transform = '';
      });

      // Restore body scrolling
      document.body.style.overflow = '';

      // Execute callback if provided
      if (typeof callback === 'function') {
        callback();
      }
    }, 300);
  }

  // Remove any existing event listeners to prevent duplicates
  const newButton = userMenuButton.cloneNode(true);
  userMenuButton.parentNode.replaceChild(newButton, userMenuButton);

  // Update reference to the new button
  const cleanUserMenuButton = document.getElementById('user-menu-button');

  // Toggle menu when clicking the button
  cleanUserMenuButton.addEventListener('click', (event) => {
    console.log('User menu button clicked');
    event.preventDefault();
    event.stopPropagation();

    const isCurrentlyShown = userMenu.classList.contains('show');
    console.log('Menu currently shown:', isCurrentlyShown);

    if (isCurrentlyShown) {
      console.log('Hiding menu');
      hideMenu();
    } else {
      console.log('Showing menu');
      menuJustOpened = true;
      showMenu();
    }
  });

  // Close menu when clicking on backdrop
  userMenuBackdrop.addEventListener('click', (event) => {
    console.log('Backdrop clicked');
    event.stopPropagation();
    hideMenu();
  });

  // Close menu when clicking the close button
  if (userMenuClose) {
    userMenuClose.addEventListener('click', () => {
      hideMenu();
    });
  }

  // Add Edit Profile click handler
  editProfile.addEventListener('click', async function(event) {
    event.preventDefault();
    const user = firebase.auth().currentUser;
    if (user && typeof handleAccountManagement === 'function') {
      // Pass the account management function as a callback to hideMenu
      hideMenu(() => {
        handleAccountManagement(user);
      });
    } else {
      hideMenu();
    }
  });

  // Handle logout
  userLogout.addEventListener('click', async function(event) {
    console.log('Logout clicked');
    event.preventDefault();
    hideMenu();

    const user = firebase.auth().currentUser;
    if (user) {
      if (typeof handleUserFeedback === 'function') {
        const shouldProceed = await handleUserFeedback(user);
        if (shouldProceed) {
          firebase.auth().signOut().then(() => {
            console.log('User signed out');
            if (typeof cleanupCreditListener === 'function') {
              cleanupCreditListener();
            }
            window.location.href = 'index.html';
          }).catch((error) => {
            console.error('Sign out error', error);
          });
        }
      } else {
        firebase.auth().signOut().then(() => {
          console.log('User signed out');
          window.location.href = 'index.html';
        }).catch((error) => {
          console.error('Sign out error', error);
        });
      }
    } else {
      console.error('No user is currently signed in');
    }
  });

  // Improve keyboard accessibility
  cleanUserMenuButton.addEventListener('keydown', (event) => {
    if (event.key === 'Enter' || event.key === ' ') {
      event.preventDefault();
      menuJustOpened = true;
      showMenu();
      const firstFocusableElement = userMenu.querySelector('a, button:not([disabled])');
      if (firstFocusableElement) {
        firstFocusableElement.focus();
      }
    }
  });

  // Add escape key support
  document.addEventListener('keydown', (event) => {
    if (event.key === 'Escape' && userMenu.classList.contains('show')) {
      hideMenu();
      cleanUserMenuButton.focus();
    }
  });

  // Flag to prevent immediate closing after opening
  let menuJustOpened = false;

  // Close menu when clicking outside
  document.addEventListener('click', (event) => {
    if (userMenu.classList.contains('show') && !menuJustOpened) {
      // Check if the click is outside the menu and not on the menu button
      if (!userMenu.contains(event.target) && !cleanUserMenuButton.contains(event.target)) {
        console.log('Document click outside menu, closing menu');
        hideMenu();
      }
    }
    // Reset the flag after any click
    menuJustOpened = false;
  });

  // Add responsive positioning for different screen sizes
  window.addEventListener('resize', () => {
    if (userMenu.classList.contains('show')) {
      const buttonRect = userMenuButton.getBoundingClientRect();
      userMenu.style.top = `${buttonRect.bottom + 8}px`;
      userMenu.style.right = `0px`;
    }
  });

  // Set default user info with updated styling
  function setDefaultUserInfo(userMenu) {
    const nameElement = userMenu.querySelector('p.font-semibold');
    const companyElement = userMenu.querySelector('p.text-xs');
    const avatarElement = userMenu.querySelector('.avatar-container img');
    const statusElement = userMenu.querySelector('.inline-flex.items-center span.text-xs');

    if (nameElement) nameElement.textContent = 'Guest User';
    if (companyElement) companyElement.textContent = 'Not logged in';
    if (avatarElement) avatarElement.src = 'profile.png';
    if (statusElement) {
      statusElement.textContent = 'Inactive';
      statusElement.style.fontSize = '11px'; // Smaller text size for status
    }
  }

  // Listen for auth state changes
  firebase.auth().onAuthStateChanged((user) => {
    if (user) {
      console.log('User is signed in:', user.email);
      setupProfileListener(user);
      setupUserDataListener();
      populateUserInfo(user, userMenu);

      // Store the listener for cleanup
      userMenu.setAttribute('data-profile-listener', 'active');
    } else {
      console.log('No user is signed in');
      // Clean up listener if exists
      if (userMenu.getAttribute('data-profile-listener') === 'active') {
        userMenu.removeAttribute('data-profile-listener');
      }
      setDefaultUserInfo(userMenu);
    }
  });

  // Setup profile listener
  function setupProfileListener(user) {
    if (!user) return;

    const db = firebase.firestore();
    const adminRef = db.collection('Admins').doc(user.email);
    return adminRef.onSnapshot((doc) => {
      if (doc.exists) {
        const adminData = doc.data();
        updateUserMenuUI(user, adminData, userMenu);
      }
    }, (error) => {
      console.error('Error listening to profile changes:', error);
    });
  }

  // Function to update UI elements with modernized styling
  function updateUserMenuUI(user, adminData, userMenu) {
    const nameElement = userMenu.querySelector('p.font-semibold');
    const companyElement = userMenu.querySelector('p.text-xs');
    const avatarElement = userMenu.querySelector('.avatar-container img');
    const statusElement = userMenu.querySelector('.inline-flex.items-center span.text-xs');

    if (nameElement) {
      nameElement.textContent = `${adminData.firstname} ${adminData.lastname}`;
    }
    if (companyElement) {
      companyElement.textContent = adminData.company || window.userCompany;
    }
    if (avatarElement) {
      avatarElement.src = user.photoURL || adminData.profilePicture || 'profile.png';
      avatarElement.style.transition = 'transform 0.3s ease';
      avatarElement.onmouseover = () => { avatarElement.style.transform = 'scale(1.05)'; };
      avatarElement.onmouseout = () => { avatarElement.style.transform = 'scale(1)'; };
    }
    if (statusElement) {
      statusElement.textContent = adminData.status || 'Active';
      statusElement.style.fontSize = '11px'; // Smaller text size for status
    }

    // Update menu avatar as well
    const menuAvatar = document.getElementById('user-menu-avatar');
    if (menuAvatar) {
      menuAvatar.src = user.photoURL || adminData.profilePicture || 'profile.png';
    }
  }

  // Setup real-time listener for user data changes
  function setupUserDataListener() {
    const currentUser = firebase.auth().currentUser;
    if (!currentUser) {
      console.error('No user logged in');
      return;
    }

    const db = firebase.firestore();
    const adminRef = db.collection('Admins').doc(currentUser.email);
    adminRef.onSnapshot((doc) => {
      if (doc.exists) {
        const adminData = doc.data();

        // Update credits
        const creditsSection = userMenu.querySelector('.credits-section');
        if (creditsSection) {
          const creditsBalance = creditsSection.querySelector('.font-semibold');
          if (creditsBalance) {
            creditsBalance.textContent = adminData.credits || 0;
          }
        }

        // Update subscription info
        updateSubscriptionInfo(adminData);

        console.log('User data updated from real-time listener:', {
          subscriptionType: adminData.subscriptionType,
          credits: adminData.credits,
          subscriptionEndDate: adminData.subscriptionEndDate ?
            (adminData.subscriptionEndDate.toDate ? adminData.subscriptionEndDate.toDate().toISOString() : adminData.subscriptionEndDate) : null,
          pendingSubscriptionChange: adminData.pendingSubscriptionChange ? 'Yes' : 'No'
        });
      }
    }, (error) => {
      console.error('Error listening to user data changes:', error);
    });
  }

  // Function to populate user info
  function populateUserInfo(user, userMenu) {
    const db = firebase.firestore();
    const adminRef = db.collection('Admins').doc(user.email);
    adminRef.get().then((doc) => {
      if (doc.exists) {
        const adminData = doc.data();

        // Store the original company name for demo mode toggling
        if (adminData.company) {
          window.originalUserCompany = adminData.company;
          // If not in demo mode, set the current company
          if (!window.isDemoMode) {
            window.userCompany = adminData.company;
          }
        }

        // Update basic user info
        updateUserMenuUI(user, adminData, userMenu);

        // Update subscription info
        updateSubscriptionInfo(adminData);

        // Update credits info
        updateCreditsInfo(adminData.credits || 0);

        // Setup demo mode if needed
        setupDemoMode(adminData);

        // Setup real-time listener for user data changes
        setupUserDataListener();
      }
    }).catch((error) => {
      console.error('Error fetching admin data:', error);
      setDefaultUserInfo(userMenu);
    });
  }

  // Function to update subscription info
  function updateSubscriptionInfo(adminData) {
    const subscriptionSection = userMenu.querySelector('.subscription-section');
    if (!subscriptionSection) return;

    const subscriptionLabel = subscriptionSection.querySelector('.font-semibold');
    if (!subscriptionLabel) return;

    // Get subscription status
    // Only use 'Free Trial' as default if subscriptionType is not null
    const subscriptionType = adminData.subscriptionType !== null ? (adminData.subscriptionType || 'Free Trial') : 'No Subscription';
    const isSubscriptionActive = adminData.subscriptionActive || false;
    const hasCancelRequest = adminData.subscriptionCancelRequested || false;
    const isCanceled = hasCancelRequest && isSubscriptionActive && adminData.subscriptionEndDate;

    // Ensure consistent free trial detection - only if subscriptionType is actually set
    const isFreeTrial = adminData.subscriptionType && (
                       subscriptionType === 'freeTrial' ||
                       subscriptionType.toLowerCase() === 'freetrial' ||
                       subscriptionType.toLowerCase() === 'free trial');

    // Store for global access
    window.isFreeTrial = isFreeTrial;

    console.log('Subscription status initialized:', {
      subscriptionType: subscriptionType,
      isSubscriptionActive: isSubscriptionActive,
      hasCancelRequest: hasCancelRequest,
      isCanceled: isCanceled,
      isFreeTrial: isFreeTrial
    });

    // Update Add Credits button visibility based on subscription type
    const addCreditsButtonContainer = document.getElementById('add-credits-button-container');
    if (addCreditsButtonContainer) {
      if (isSubscriptionActive && !isFreeTrial) {
        // Show Add Credits button for paid users
        addCreditsButtonContainer.classList.remove('hidden');
      } else {
        // Hide Add Credits button for free trial users
        addCreditsButtonContainer.classList.add('hidden');
      }
    }

    // Update subscription label
    if (adminData.subscriptionType === null) {
      subscriptionLabel.textContent = 'No Subscription';
      subscriptionLabel.classList.add('text-gray-500');
    } else {
      subscriptionLabel.textContent = `${subscriptionType}${isCanceled ? ' (Cancelled)' : ''}`;

      // Add appropriate color class based on subscription status
      if (isFreeTrial) {
        subscriptionLabel.classList.add('text-blue-600');
      } else if (isCanceled) {
        subscriptionLabel.classList.add('text-orange-500');
      } else if (isSubscriptionActive) {
        subscriptionLabel.classList.add('text-green-600');
      } else {
        subscriptionLabel.classList.add('text-red-500');
      }
    }

    // Get UI elements
    const defaultButtons = document.getElementById('default-subscription-buttons');
    const cancellationButtons = document.getElementById('cancellation-subscription-buttons');
    const subscriptionEndNotice = document.getElementById('subscription-end-notice');
    const subscriptionEndDate = document.getElementById('subscription-end-date');

    // Handle different subscription states
    if (adminData.subscriptionType === null) {
      // No subscription selected yet
      if (defaultButtons) defaultButtons.classList.remove('hidden');
      if (cancellationButtons) cancellationButtons.classList.add('hidden');

      // Always hide subscription end notice for users with no subscription
      if (subscriptionEndNotice) subscriptionEndNotice.classList.add('hidden');

      // Clear any existing subscription end date text to prevent misleading information
      if (subscriptionEndDate) {
        subscriptionEndDate.textContent = '';
      }
    } else if (isCanceled && adminData.subscriptionEndDate) {
      // Show cancellation UI
      if (defaultButtons) defaultButtons.classList.add('hidden');
      if (cancellationButtons) cancellationButtons.classList.remove('hidden');

      // Format and show end date - ALWAYS show for canceled subscriptions
      if (subscriptionEndNotice && subscriptionEndDate) {
        const endDate = adminData.subscriptionEndDate.toDate ?
          adminData.subscriptionEndDate.toDate() :
          new Date(adminData.subscriptionEndDate);

        const formattedDate = formatDate(endDate);
        const daysLeft = getDaysUntilSubscriptionEnds(adminData.subscriptionEndDate);
        const daysLeftText = daysLeft ? ` (${daysLeft})` : '';

        // Make sure the end date is in the future
        const now = new Date();
        if (endDate > now) {
          subscriptionEndDate.textContent = `Ends on ${formattedDate}${daysLeftText}`;
        } else {
          subscriptionEndDate.textContent = `Ended on ${formattedDate}`;
        }

        // Always show the notice for canceled subscriptions
        subscriptionEndNotice.classList.remove('hidden');
        console.log('Showing end date for canceled subscription');
      }
    } else if (isFreeTrial) {
      // Show default UI for free trial
      if (defaultButtons) defaultButtons.classList.remove('hidden');
      if (cancellationButtons) cancellationButtons.classList.add('hidden');

      console.log('Free trial detected:', {
        subscriptionType: adminData.subscriptionType,
        endDate: adminData.subscriptionEndDate,
        isFreeTrial: isFreeTrial
      });

      // Format and show trial end date
      if (subscriptionEndNotice && subscriptionEndDate) {
        // Always show the free trial end date information
        if (adminData.subscriptionEndDate) {
          const endDate = adminData.subscriptionEndDate.toDate ?
            adminData.subscriptionEndDate.toDate() :
            new Date(adminData.subscriptionEndDate);

          const formattedDate = formatDate(endDate);
          const daysLeft = getDaysUntilSubscriptionEnds(adminData.subscriptionEndDate);
          const daysLeftText = daysLeft ? ` (${daysLeft})` : '';

          // Make sure the end date is in the future
          const now = new Date();
          if (endDate > now) {
            subscriptionEndDate.textContent = `Trial ends on ${formattedDate}${daysLeftText}`;
          } else {
            subscriptionEndDate.textContent = `Trial ended on ${formattedDate}`;
          }
          subscriptionEndNotice.classList.remove('hidden');
        } else {
          // If no end date is set (shouldn't happen), show a generic message
          subscriptionEndDate.textContent = 'Trial ends in 14 days';
          subscriptionEndNotice.classList.remove('hidden');
        }
      }
    } else if (isSubscriptionActive) {
      // Show default UI for active subscription
      if (defaultButtons) defaultButtons.classList.remove('hidden');
      if (cancellationButtons) cancellationButtons.classList.add('hidden');

      // Format and show renewal/end date
      if (subscriptionEndNotice && subscriptionEndDate) {
        // Check if we have a date to display AND user has a subscription type set
        // This prevents showing end dates for users with no subscription
        if (adminData.subscriptionEndDate && adminData.subscriptionType !== null) {
          const endDate = adminData.subscriptionEndDate.toDate ?
            adminData.subscriptionEndDate.toDate() :
            new Date(adminData.subscriptionEndDate);

          const formattedDate = formatDate(endDate);
          const daysLeft = getDaysUntilSubscriptionEnds(adminData.subscriptionEndDate);

          const now = new Date();
          const daysDifference = Math.ceil((endDate - now) / (1000 * 60 * 60 * 24));

          // Determine if we should show days left text
          // Only show for dates less than 30 days away or more than 365 days away
          const daysLeftText = (daysLeft && (daysDifference <= 30 || daysDifference > 365)) ?
                                ` (${daysLeft})` : '';

          // Determine the correct prefix based on subscription state and type
          let prefix = 'Ends on';

          // Use the isFreeTrial variable defined at the top of the function
          // DO NOT redefine it here to avoid scope issues
          console.log('Free trial check in active subscription section:', {
            subscriptionType: adminData.subscriptionType,
            isFreeTrial: isFreeTrial,
            globalIsFreeTrial: window.isFreeTrial
          });

          // Make sure the date is in the future
          if (endDate > now) {
            // For active subscriptions that aren't canceled
            if (isSubscriptionActive && !isCanceled) {
              // Check if we have a yearly subscription
              if (adminData.subscriptionInterval === 'yearly' ||
                  adminData.subscriptionType.toLowerCase().includes('yearly') ||
                  (adminData.subscriptionEndDate && daysDifference > 180)) { // If end date is >6 months away, likely yearly
                prefix = 'Renews on';
                console.log('Displaying yearly subscription renewal date');
              } else if (adminData.subscriptionRenewalDate) {
                // For monthly subscriptions with renewal dates
                prefix = 'Renews on';
                console.log('Displaying monthly subscription renewal date');
              }
            }

            // Set the text content
            subscriptionEndDate.textContent = `${prefix} ${formattedDate}${daysLeftText}`;

            // Determine whether to show or hide the notice
            // Show if:
            // 1. It's a free trial, OR
            // 2. It's a canceled subscription, OR
            // 3. The subscription ends in 10 days or less

            // Add more detailed logging to debug the condition
            console.log('Subscription notice conditions:', {
              isFreeTrial: isFreeTrial,
              isCanceled: isCanceled,
              daysDifference: daysDifference,
              shouldShow: isFreeTrial || isCanceled || daysDifference <= 10,
              subscriptionType: adminData.subscriptionType,
              subscriptionInterval: adminData.subscriptionInterval
            });

            // Force re-evaluation of the condition
            const shouldShowNotice = (isFreeTrial === true) || (isCanceled === true) || (daysDifference <= 10);

            if (shouldShowNotice) {
              subscriptionEndNotice.classList.remove('hidden');
              console.log(`Showing end date notice: ${isFreeTrial ? 'Free trial' : isCanceled ? 'Canceled' : 'Ending soon'}`);
            } else {
              // Hide for active subscriptions with more than 10 days remaining
              subscriptionEndNotice.classList.add('hidden');
              console.log(`Hiding end date notice: ${daysDifference} days remaining`);
            }
          } else {
            // If the date is in the past, show "Ended on"
            subscriptionEndDate.textContent = `Ended on ${formattedDate}`;
            subscriptionEndNotice.classList.remove('hidden');
          }
        } else {
          // If no date is set, hide the notice
          subscriptionEndNotice.classList.add('hidden');
        }
      }
    } else {
      // Hide subscription notice for other cases
      if (defaultButtons) defaultButtons.classList.remove('hidden');
      if (cancellationButtons) cancellationButtons.classList.add('hidden');
      if (subscriptionEndNotice) subscriptionEndNotice.classList.add('hidden');

      // Clear any existing subscription end date text to prevent misleading information
      if (subscriptionEndDate) {
        subscriptionEndDate.textContent = '';
      }
    }

    // Add event listener to manage subscription button
    const manageSubscriptionBtn = document.getElementById('manageSubscriptionBtn');
    if (manageSubscriptionBtn) {
      manageSubscriptionBtn.addEventListener('click', (e) => {
        e.preventDefault();
        e.stopPropagation();

        // Show subscription modal in management mode after menu closes
        if (window.SubscriptionModal) {
          // If user has no subscription type set, show regular subscription modal
          // Otherwise show management mode
          const managementMode = adminData.subscriptionType !== null;
          hideMenu(() => {
            window.SubscriptionModal.show(managementMode, false, true);
          });
        } else {
          hideMenu();
        }
      });
    }

    // Add event listener to add credits button
    const addCreditsBtn = document.getElementById('addCreditsBtn');
    if (addCreditsBtn) {
      addCreditsBtn.addEventListener('click', (e) => {
        e.preventDefault();
        e.stopPropagation();

        // Show top-up modal after menu closes
        if (window.TopupModal) {
          hideMenu(() => {
            window.TopupModal.show();
          });
        } else {
          hideMenu();
          console.error('TopupModal not found');
          if (typeof showNotification === 'function') {
            showNotification('Unable to load top-up options. Please try again later.', 'error');
          }
        }
      });
    }

    // Add event listener to resubscribe button
    const resubscribeBtn = document.getElementById('resubscribeBtn');
    if (resubscribeBtn) {
      // Remove any existing event listeners to prevent duplicates
      const newResubscribeBtn = resubscribeBtn.cloneNode(true);
      resubscribeBtn.parentNode.replaceChild(newResubscribeBtn, resubscribeBtn);

      // Track if reactivation is in progress to prevent multiple requests
      window.reactivationInProgress = false;

      newResubscribeBtn.addEventListener('click', async (e) => {
        e.preventDefault();
        e.stopPropagation();

        // Prevent multiple clicks/requests
        if (window.reactivationInProgress) {
          console.log('Reactivation already in progress, ignoring click');
          return;
        }

        console.log('Resubscribe button clicked, starting reactivation process');
        window.reactivationInProgress = true;

        // Close menu first, then handle resubscription
        hideMenu(async () => {
          try {
            // Show loading overlay
            if (typeof showLoadingOverlay === 'function') {
              showLoadingOverlay();
              console.log('Loading overlay shown');
            }

            console.log('Sending reactivation request to server for:', {
              userId: adminData.email,
              subscriptionId: adminData.subscriptionId
            });

            // Call the server endpoint to reactivate the subscription
            const response = await fetch('/reactivate-subscription', {
              method: 'POST',
              headers: { 'Content-Type': 'application/json' },
              body: JSON.stringify({
                userId: adminData.email,
                subscriptionId: adminData.subscriptionId
              })
            });

            console.log('Server response received:', {
              status: response.status,
              statusText: response.statusText
            });

            // Check for HTTP errors before parsing JSON
            if (!response.ok) {
              throw new Error(`Server returned ${response.status}: ${response.statusText}`);
            }

            // Parse response as JSON
            let result;
            try {
              result = await response.json();
              console.log('Response parsed successfully:', result);
            } catch (jsonError) {
              console.error('Error parsing server response:', jsonError);
              throw new Error('Invalid server response');
            }

            // Check for error in result
            if (result.error) {
              throw new Error(result.error);
            }

            // Hide loading overlay before showing success notification
            if (typeof hideLoadingOverlay === 'function') {
              hideLoadingOverlay();
              console.log('Loading overlay hidden');
            }

            // Show success message
            if (typeof showNotification === 'function') {
              showNotification('Subscription successfully reactivated!', 'success');
              console.log('Success notification shown');
            }

            console.log('Scheduling page reload in 3 seconds');
            // Reload the page after a delay to refresh subscription status
            setTimeout(() => {
              console.log('Reloading page now');
              window.location.reload();
            }, 3000);
          } catch (error) {
            console.error('Error reactivating subscription:', error);

            // Always hide the loading overlay on error
            if (typeof hideLoadingOverlay === 'function') {
              hideLoadingOverlay();
              console.log('Loading overlay hidden after error');
            }

            // Show error notification
            if (typeof showNotification === 'function') {
              showNotification('Failed to reactivate subscription. Please try again.', 'error');
              console.log('Error notification shown');
            }

            // Reset the in-progress flag on error
            window.reactivationInProgress = false;
          }
        });
      });
    }
  }

  // Helper function to calculate days left until subscription ends
  function getDaysUntilSubscriptionEnds(endDate) {
    if (!endDate) return null;

    // Convert to Date object if it's a Firestore timestamp
    const subscriptionEndDate = endDate.toDate ? endDate.toDate() : new Date(endDate);

    // Check if the date is valid
    if (isNaN(subscriptionEndDate.getTime())) {
      console.error('Invalid subscription end date:', endDate);
      return null;
    }

    const now = new Date();

    // Check if the end date is set to end of day (23:59:59)
    const isEndOfDay = subscriptionEndDate.getHours() === 23 &&
                       subscriptionEndDate.getMinutes() >= 59 &&
                       subscriptionEndDate.getSeconds() >= 59;

    // For end-of-day dates (like free trial end dates), use date difference without time
    if (isEndOfDay) {
      // Create dates with only year, month, and day for accurate day calculation
      const nowDate = new Date(now.getFullYear(), now.getMonth(), now.getDate());
      const endDate = new Date(subscriptionEndDate.getFullYear(), subscriptionEndDate.getMonth(), subscriptionEndDate.getDate());

      // Calculate days difference in milliseconds and convert to days
      const diffTime = endDate.getTime() - nowDate.getTime();
      const daysLeft = Math.round(diffTime / (1000 * 60 * 60 * 24));

      console.log('Days left calculation (end-of-day method):', {
        endDate: subscriptionEndDate.toISOString(),
        now: now.toISOString(),
        daysLeft: daysLeft
      });

      return formatDaysLeft(daysLeft);
    }

    // For other dates, use the original midnight-to-midnight calculation
    now.setHours(0, 0, 0, 0);
    const endDateMidnight = new Date(subscriptionEndDate);
    endDateMidnight.setHours(0, 0, 0, 0);

    // Calculate days difference (more accurate than dividing by milliseconds)
    const diffTime = endDateMidnight.getTime() - now.getTime();
    const daysLeft = Math.ceil(diffTime / (1000 * 60 * 60 * 24));

    console.log('Days left calculation (standard method):', {
      endDate: subscriptionEndDate.toISOString(),
      now: now.toISOString(),
      daysLeft: daysLeft
    });

    return formatDaysLeft(daysLeft);
  }

  // Helper function to format days left into a readable string
  function formatDaysLeft(daysLeft) {
    if (daysLeft <= 0) return 'Today';
    if (daysLeft === 1) return 'Tomorrow';
    if (daysLeft < 30) return `${daysLeft} days`;
    if (daysLeft < 60) return 'About 1 month';
    if (daysLeft < 365) return `About ${Math.round(daysLeft / 30)} months`;
    return `About ${Math.round(daysLeft / 365)} year${Math.round(daysLeft / 365) !== 1 ? 's' : ''}`;
  }

  // Helper function to format dates consistently
  function formatDate(date) {
    if (!date) return '';

    try {
      // Format as MM/DD/YYYY
      const month = date.getMonth() + 1;
      const day = date.getDate();
      const year = date.getFullYear();

      return `${month}/${day}/${year}`;
    } catch (error) {
      console.error('Error formatting date:', error, date);
      return 'Invalid date';
    }
  }

  // Function to update credits info
  function updateCreditsInfo(credits) {
    const creditsSection = userMenu.querySelector('.credits-section');
    if (!creditsSection) return;

    const creditsBalance = creditsSection.querySelector('.font-semibold');
    if (creditsBalance) {
      creditsBalance.textContent = credits;
    }
  }

  // Function to setup demo mode
  function setupDemoMode(adminData) {
    const demoSection = userMenu.querySelector('.demo-section');
    if (!demoSection) return;

    let demoToggle = document.getElementById('demo-toggle');
    const demoToggleSwitch = document.getElementById('demo-toggle-switch');
    const modeStatus = document.getElementById('mode-status');

    // Make sure we have the original company name stored
    if (adminData && adminData.company && !window.originalUserCompany) {
      window.originalUserCompany = adminData.company;
    }

    // Set the initial state of the toggle based on window.isDemoMode
    if (demoToggle && window.isDemoMode) {
      demoToggle.checked = true;
      if (demoToggleSwitch) {
        demoToggleSwitch.classList.remove('bg-gray-200');
        demoToggleSwitch.classList.add('bg-blue-600');
        const dot = demoToggleSwitch.querySelector('.dot');
        if (dot) dot.style.transform = 'translateX(18px)';
      }
      if (modeStatus) modeStatus.textContent = 'Demo Mode';
    }

    if (demoToggle && demoToggleSwitch) {
      // Remove any existing event listeners to prevent duplicates
      const newToggle = demoToggle.cloneNode(true);
      demoToggle.parentNode.replaceChild(newToggle, demoToggle);
      demoToggle = newToggle;

      demoToggle.addEventListener('change', function() {
        const dot = demoToggleSwitch.querySelector('.dot');

        // Update global state and company
        window.isDemoMode = this.checked;
        window.userCompany = this.checked ? 'Barefoot eLearning' : window.originalUserCompany;

        // Log the state for debugging
        console.log('Demo mode toggled:', {
          isDemoMode: window.isDemoMode,
          userCompany: window.userCompany,
          originalUserCompany: window.originalUserCompany
        });

        // Update company display in UI
        const companyElement = userMenu.querySelector('p.text-xs');
        if (companyElement) {
          companyElement.textContent = window.userCompany;
        }

        // Update mode status text
        if (modeStatus) {
          modeStatus.textContent = this.checked ? 'Demo Mode' : 'Live Mode';
        }

        // Update toggle switch appearance
        if (this.checked) {
          demoToggleSwitch.classList.remove('bg-gray-200');
          demoToggleSwitch.classList.add('bg-blue-600');
          dot.style.transform = 'translateX(18px)';
        } else {
          demoToggleSwitch.classList.add('bg-gray-200');
          demoToggleSwitch.classList.remove('bg-blue-600');
          dot.style.transform = 'translateX(0)';
        }

        // Update navigation state if function exists
        if (typeof window.updateNavigationState === 'function') {
          window.updateNavigationState();
        }

        // Clear cached data to force reload with new company
        sessionStorage.removeItem('dashboardData');
        sessionStorage.removeItem('dashboardLastFetchTime');
        sessionStorage.removeItem('tableData');
        sessionStorage.removeItem('tableLastFetchTime');
        sessionStorage.removeItem('assessmentsData');
        sessionStorage.removeItem('assessmentsLastFetchTime');

        // Load appropriate page based on mode
        const mainContent = document.querySelector('#main-content');
        if (this.checked) {
          if (typeof loadDashboardPage === 'function') {
            loadDashboardPage(mainContent);
          }
          if (typeof updateActiveNavLink === 'function') {
            updateActiveNavLink('dashboard');
          }
          if (typeof showToast === 'function') {
            showToast('Switched to demo mode');
          }
        } else {
          if (typeof loadInvitePage === 'function') {
            loadInvitePage(mainContent);
          }
          if (typeof updateActiveNavLink === 'function') {
            updateActiveNavLink('invitations');
          }
          if (typeof showToast === 'function') {
            showToast('Switched to live mode');
          }
        }
      });
    }
  }
}

// Initialize when the DOM is loaded
document.addEventListener('DOMContentLoaded', initializeUserMenu);
